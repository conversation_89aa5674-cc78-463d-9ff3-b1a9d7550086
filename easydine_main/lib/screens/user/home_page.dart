import 'dart:ui';
import 'package:easydine_main/blocs/session/session_state.dart';
import 'package:easydine_main/blocs/settings/settings_bloc.dart';
import 'package:easydine_main/widgets/settings_drawer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../blocs/session/session_bloc.dart';
import '../../blocs/session/session_event.dart';
import '../../router/router_constants.dart';
import '../../widgets/tiled_background.dart';

class MenuButton {
  final IconData icon;
  final String label;
  final VoidCallback onTap;
  final bool isHighlighted;

  const MenuButton({
    required this.icon,
    required this.label,
    required this.onTap,
    this.isHighlighted = false,
  });
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  static const List<String> _checklistItems = [
    'Opening Checks',
    'Chilled Storage Checks',
    'Cooking Temperature Checks',
    'Cooling Checks',
    'Reheating Temperature Checks',
  ];

  bool _showChecklistReminder = false;

  @override
  void initState() {
    super.initState();
    _checkForSkippedChecklist();
  }

  Future<void> _checkForSkippedChecklist() async {
    final prefs = await SharedPreferences.getInstance();
    final isSkipped = prefs.getBool('checklist_skipped') ?? false;

    final allCompleted = _checklistItems.every((item) =>
    prefs.getBool('checklist_$item') ?? false);

    if (mounted) {
      setState(() {
        _showChecklistReminder = isSkipped || !allCompleted;
      });
    }
  }

  List<MenuButton> _getMenuButtons() {
    return [
      MenuButton(
        icon: Icons.point_of_sale,
        label: 'POS',
        onTap: () => _showPOSOptions(context),
      ),
      MenuButton(
        icon: Icons.receipt_long_outlined,
        label: 'Running Orders',
        onTap: () => context.goNamed(RouterConstants.runningOrders),
      ),
      MenuButton(
        icon: Icons.timer,
        label: 'Reservations',
        onTap: () => context.goNamed(RouterConstants.dailyChecklist),
      ),
      MenuButton(
        label: 'Bulk Orders',
        icon: Icons.shopping_cart_outlined,
        onTap: () => context.goNamed(RouterConstants.pos),
      ),
      MenuButton(
        icon: Icons.analytics_outlined,
        label: 'Reports',
        onTap: () => context.goNamed(RouterConstants.reports),
      ),
      MenuButton(
        icon: Icons.help_outline_outlined,
        label: 'Support',
        onTap: () => context.goNamed(RouterConstants.support),
      ),
      MenuButton(
        icon: Icons.settings_outlined,
        label: 'Settings',
        onTap: () => context.goNamed(RouterConstants.settings),
      ),

    ];
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildAppBar(),
      endDrawer: const BuildSettingsDrawer(),
      body: Stack(
        children: [
          const TiledBackground(),
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 32, sigmaY: 32),
            child: _buildBody(isLandscape),
          ),
        ],
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      leading: _buildUserProfile(),
      leadingWidth: MediaQuery.of(context).size.width * 0.2,
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
      title: _buildAppBarTitle(),
      actions: [_buildLogoutButton()],
    );
  }

  Widget _buildUserProfile() {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.2,
      ),
      child: BlocBuilder<SessionBloc, SessionState>(
        builder: (context, state) {
          final waiterName = state.waiterName ?? '';
          final firstLetter = waiterName.isNotEmpty ? waiterName[0].toUpperCase() : '?';

          return Padding(
            padding: EdgeInsets.fromLTRB(
              16,
              8,
              MediaQuery.of(context).size.width * 0.02,
              8,
            ),
            child: Row(
              children: [
                _buildUserAvatar(firstLetter),
                const SizedBox(width: 8),
                _buildUserInfo(waiterName, state.currentStaff?.role ?? 'Waiter'),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildUserAvatar(String firstLetter) {
    return Stack(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [Colors.orange.shade400, Colors.deepOrange.shade600],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Center(
            child: Text(
              firstLetter,
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        Positioned(
          right: 0,
          bottom: 0,
          child: Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserInfo(String waiterName, String role) {
    return Flexible(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Flexible(
                child: Text(
                  waiterName.isNotEmpty ? waiterName : 'Guest',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 4),
              const Icon(Icons.verified, color: Colors.blue, size: 14),
            ],
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.purple.withOpacity(0.3),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              role,
              style: GoogleFonts.poppins(
                color: Colors.white70,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppBarTitle() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(Icons.restaurant_menu, color: Colors.white),
        const SizedBox(width: 8),
        Text(
          'Restaurant POS',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.w600,
            fontSize: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      margin: const EdgeInsets.only(right: 16, left: 8),
      child: IconButton(
        icon: const Icon(Icons.power_settings_new),
        tooltip: 'Logout',
        style: IconButton.styleFrom(
          backgroundColor: Colors.red.withOpacity(0.2),
          foregroundColor: Colors.red[400],
          padding: const EdgeInsets.all(8),
        ),
        onPressed: () => _showLogoutConfirmation(context),
      ),
    );
  }

  Widget _buildBody(bool isLandscape) {
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, state) {
        final menuButtons = _getMenuButtons();
        if (state.rushOrderEnabled) {
          menuButtons.insert(2, MenuButton(
            icon: Icons.speed_outlined,
            label: 'Rush Orders',
            onTap: () => context.goNamed(RouterConstants.rushOrders),
            isHighlighted: true,
          ));
        }

        return SafeArea(
          child: Column(
            children: [
              if (_showChecklistReminder) _buildChecklistReminder(),
              Expanded(
                child: _buildMenuGrid(menuButtons, isLandscape),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildChecklistReminder() {
    return GestureDetector(
      onTap: () => context.goNamed(RouterConstants.dailyChecklist),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.orange.withOpacity(0.9),
              Colors.deepOrange.withOpacity(0.9),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: const [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 8,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            const Icon(Icons.warning_amber_rounded, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Daily checklist is incomplete. Tap to complete now.',
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const Icon(Icons.arrow_forward_ios, color: Colors.white, size: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuGrid(List<MenuButton> buttons, bool isLandscape) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: isLandscape
              ? MediaQuery.of(context).size.width * 0.6
              : MediaQuery.of(context).size.width * 0.8,
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: GridView.count(
            shrinkWrap: true,
            crossAxisCount: 4,
            mainAxisSpacing: 20,
            crossAxisSpacing: 20,
            children: buttons.map((button) => _buildMenuButton(button)).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildMenuButton(MenuButton button) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: button.onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: button.isHighlighted ? Colors.orange : Colors.white24,
              width: button.isHighlighted ? 2 : 1,
            ),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.1),
                Colors.white.withOpacity(0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                button.icon,
                size: 32,
                color: button.isHighlighted ? Colors.orange : Colors.white,
              ),
              const SizedBox(height: 12),
              Text(
                button.label,
                style: GoogleFonts.poppins(
                  color: button.isHighlighted ? Colors.orange : Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPOSOptions(BuildContext context) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) => Container(),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: Tween<double>(begin: 0.5, end: 1.0).animate(
            CurvedAnimation(parent: animation, curve: Curves.easeOutBack),
          ),
          child: FadeTransition(
            opacity: animation,
            child: _buildPOSDialog(isLandscape),
          ),
        );
      },
    );
  }

  Widget _buildPOSDialog(bool isLandscape) {
    final posOptions = [
      _POSOption(
        icon: Icons.restaurant_outlined,
        label: 'Dine In',
        description: 'Table service orders',
        onTap: () {
          Navigator.pop(context);
          context.goNamed(RouterConstants.dineIn);
        },
      ),
      _POSOption(
        icon: Icons.delivery_dining_outlined,
        label: 'Delivery',
        description: 'Home delivery orders',
        onTap: () {
          Navigator.pop(context);
          context.goNamed(RouterConstants.delivery);
        },
      ),
      _POSOption(
        icon: Icons.takeout_dining_outlined,
        label: 'Take Away',
        description: 'Pickup orders',
        onTap: () {
          Navigator.pop(context);
          context.goNamed(RouterConstants.pos, queryParameters: {
            'tableNumber': '1',
            'orderId': '1',
            'orderType': 'takeaway',
          });
        },
      ),
      _POSOption(
        icon: Icons.qr_code_scanner_outlined,
        label: 'Contactless',
        description: 'QR code ordering',
        onTap: () {
          Navigator.pop(context);
          context.goNamed(RouterConstants.contactlessDining);
        },
      ),
    ];

    return Dialog(
      backgroundColor: Colors.grey[900],
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: isLandscape
              ? MediaQuery.of(context).size.width * 0.6
              : MediaQuery.of(context).size.width * 0.8,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDialogHeader(),
              const SizedBox(height: 24),
              GridView.count(
                shrinkWrap: true,
                crossAxisCount: 2,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                childAspectRatio: isLandscape ? 2 : 1.2,
                children: posOptions.map((option) => _buildPOSOptionTile(option)).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDialogHeader() {
    return Row(
      children: [
        const Icon(Icons.point_of_sale, color: Colors.white, size: 28),
        const SizedBox(width: 12),
        Text(
          'Select Order Type',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close, color: Colors.white70),
          onPressed: () => Navigator.pop(context),
        ),
      ],
    );
  }

  Widget _buildPOSOptionTile(_POSOption option) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: option.onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white24),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.1),
                Colors.white.withOpacity(0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(option.icon, color: Colors.white, size: 32),
              ),
              const SizedBox(height: 12),
              Text(
                option.label,
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                option.description,
                style: GoogleFonts.poppins(
                  color: Colors.white70,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showLogoutConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text('Confirm Logout', style: TextStyle(color: Colors.white)),
        content: const Text(
          'Are you sure you want to logout?',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red[400]),
            onPressed: () {
              context.read<SessionBloc>().add(EndSession());
              context.goNamed(RouterConstants.pinEntry);
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}

class _POSOption {
  final IconData icon;
  final String label;
  final String description;
  final VoidCallback onTap;

  const _POSOption({
    required this.icon,
    required this.label,
    required this.description,
    required this.onTap,
  });
}