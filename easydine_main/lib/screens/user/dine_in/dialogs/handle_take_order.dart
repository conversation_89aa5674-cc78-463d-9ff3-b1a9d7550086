import 'package:easydine_main/blocs/table/table_bloc.dart';
import 'package:easydine_main/blocs/table/table_event.dart';
import 'package:easydine_main/blocs/cart/cart_bloc.dart';
import 'package:easydine_main/blocs/cart/cart_event.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../router/router_constants.dart';
import '../../../../services/table_order_service.dart';

void handleTakeOrder(BuildContext context, Map<String, dynamic> table) {
  showDialog(
    context: context,
    builder: (context) => TakeOrderDialog(table: table),
  );
}

class TakeOrderDialog extends StatefulWidget {
  final Map<String, dynamic> table;

  const TakeOrderDialog({super.key, required this.table});

  @override
  State<TakeOrderDialog> createState() => _TakeOrderDialogState();
}

class _TakeOrderDialogState extends State<TakeOrderDialog> {
  final _confirmationCodeController = TextEditingController();
  bool _isLoading = false;
  int _numOfGuests = 1; // Default to 1 guest

  @override
  void dispose() {
    _confirmationCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.4,
        decoration: BoxDecoration(
          color: const Color(0xFF1E1E1E),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.white24),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.white24)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.restaurant_menu,
                    color: const Color(0xFF2CBF5A),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Take Order - Table ${widget.table['tableNumber'] ?? widget.table['id']}',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white54),
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Number of Guests',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white10,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.white24),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed: _numOfGuests > 1
                              ? () {
                                  setState(() {
                                    _numOfGuests--;
                                  });
                                }
                              : null,
                          icon: Icon(
                            Icons.remove,
                            color:
                                _numOfGuests > 1 ? Colors.white : Colors.grey,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Text(
                            _numOfGuests.toString(),
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: _numOfGuests < 20
                              ? () {
                                  setState(() {
                                    _numOfGuests++;
                                  });
                                }
                              : null,
                          icon: Icon(
                            Icons.add,
                            color:
                                _numOfGuests < 20 ? Colors.white : Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Confirmation Code (Optional)',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _confirmationCodeController,
                    style: GoogleFonts.poppins(color: Colors.white),
                    decoration: InputDecoration(
                      hintText: 'Enter reservation confirmation code',
                      hintStyle: GoogleFonts.poppins(color: Colors.grey),
                      filled: true,
                      fillColor: Colors.white10,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide.none,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _isLoading
                              ? null
                              : () => Navigator.of(context).pop(),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Colors.white54),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: Text(
                            'Cancel',
                            style: GoogleFonts.poppins(color: Colors.white54),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _takeOrder,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF2CBF5A),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                              : Text(
                                  'Take Order',
                                  style:
                                      GoogleFonts.poppins(color: Colors.white),
                                ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _takeOrder() async {
    // Validate number of guests (should always be valid with quantity controls)
    if (_numOfGuests <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Please select a valid number of guests',
            style: GoogleFonts.poppins(),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final confirmationCode = _confirmationCodeController.text.trim();
      final result = await TableOrderService.takeOrder(
        tableId: widget.table['id'] as String,
        numOfGuests: _numOfGuests,
        confirmationCode: confirmationCode.isNotEmpty ? confirmationCode : null,
      );

      if (result != null && mounted) {
        // Extract cart details from the response
        final cartDetails = result['cartDetails'];
        final cartId = cartDetails?['cartId'] as String?;

        if (cartId != null) {
          // Activate the cart using CartBloc
          final cartBloc = context.read<CartBloc>();
          cartBloc.add(ActivateCart(cartId: cartId));
        }

        // Update table status
        final tableBloc = context.read<TableBloc>();
        tableBloc.add(MarkTableAsOccupied(
          tableId: widget.table['id'] as String,
          bookedSeats: _numOfGuests,
          customerDetails: {
            'time': DateTime.now().toString(),
            'type': 'Dine-in',
            'guests': _numOfGuests,
            if (confirmationCode.isNotEmpty)
              'confirmationCode': confirmationCode,
          },
        ));

        // Close take order dialog
        Navigator.of(context).pop();

        // Navigate directly to POS screen
        GoRouter.of(context).goNamed(
          RouterConstants.pos,
          queryParameters: {
            'tableNumber': widget.table['tableNumber']?.toString() ??
                widget.table['name']?.toString() ??
                widget.table['id'].toString(),
            'orderId': cartId ?? 'unknown',
            'orderType': 'dine_in',
          },
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Order taken successfully for $_numOfGuests guests',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: const Color(0xFF2CBF5A),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to take order. Please try again.',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error taking order: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
